/**
 * <PERSON><PERSON>ản lý workers cho BullMQ - Tối ưu hóa và chuẩn hóa
 */
import { Worker } from 'bullmq';
import { processProductSyncJob } from './processors/product-sync.processor';
import { processMessageBatch, handleFailedMessageBatch } from './processors/message-batch.processor';
import { 
  redisConnection, 
  QUEUE_NAMES, 
  workerConfigs, 
  loggingConfig,
  healthCheckConfig
} from './config';

// Interface cho worker manager
interface WorkerManager {
  worker: Worker;
  isHealthy: boolean;
  lastHealthCheck: Date;
  failedHealthChecks: number;
}

// Map để lưu trữ tất cả workers
const workers = new Map<string, WorkerManager>();

// Logging utility
const log = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logData = data && loggingConfig.logJobData ? JSON.stringify(data) : '';
  
  switch (level) {
    case 'info':
      if (loggingConfig.logLevel === 'info') {
        console.log(`[${timestamp}] ℹ️ ${message}`, logData);
      }
      break;
    case 'warn':
      console.warn(`[${timestamp}] ⚠️ ${message}`, logData);
      break;
    case 'error':
      console.error(`[${timestamp}] ❌ ${message}`, logData);
      break;
  }
};

/**
 * Tạo worker với cấu hình tối ưu và event handlers
 */
const createWorker = (queueName: string, processor: any, config: any): Worker => {
  log('info', `Đang khởi tạo worker cho queue: ${queueName}`);
  
  const worker = new Worker(queueName, processor, {
    connection: redisConnection,
    ...config,
    // Đảm bảo worker thực sự lắng nghe
    autorun: true,
    removeOnComplete: 10,
    removeOnFail: 20,
    // QUAN TRỌNG: Đảm bảo prefix khớp với queue
    prefix: process.env.BULLMQ_PREFIX || '{mooly-queue}',
  });

  // Event handlers cho worker
  worker.on('ready', () => {
    log('info', `✅ Worker ${queueName} đã sẵn sàng và đang lắng nghe jobs`);
    updateWorkerHealth(queueName, true);

    // Log worker connection status (chỉ trong development)
    if (!loggingConfig.healthCheckSilent) {
      log('info', `🔍 Worker ${queueName} connection status`, {
        isRunning: !worker.closing,
        concurrency: worker.opts.concurrency,
        prefix: worker.opts.prefix,
      });
    }
  });

  worker.on('error', (error) => {
    log('error', `❌ Lỗi worker ${queueName}:`, error);
    updateWorkerHealth(queueName, false);
  });

  worker.on('failed', (job, error) => {
    log('error', `❌ Job ${job?.id} trong ${queueName} thất bại:`, {
      jobId: job?.id,
      jobName: job?.name,
      error: error.message,
      attempts: job?.attemptsMade,
      maxAttempts: job?.opts?.attempts,
    });
  });

  worker.on('completed', (job, result) => {
    // if (loggingConfig.logLevel === 'info') {
    //   log('info', `✅ Job ${job.id} trong ${queueName} hoàn thành`, {
    //     jobId: job.id,
    //     jobName: job.name,
    //     duration: Date.now() - job.processedOn!,
    //     result: loggingConfig.logJobResult ? result : 'logged separately',
    //   });
    // }
  });

  worker.on('stalled', (jobId) => {
    log('warn', `⏰ Job ${jobId} trong ${queueName} bị stalled`);
  });

  worker.on('progress', (job, progress) => {
    // Chỉ log progress cho jobs quan trọng và trong development
    if (loggingConfig.logLevel === 'info' && !loggingConfig.healthCheckSilent) {
      log('info', `📊 Job ${job.id} tiến độ: ${progress}%`);
    }
  });

  // Log khi worker bắt đầu xử lý job
  worker.on('active', (job) => {
    if (loggingConfig.logWorkerEvents) {
      // log('info', `🔄 Worker ${queueName} bắt đầu xử lý job ${job.id}`, {
      //   jobName: job.name,
      //   jobData: loggingConfig.logJobData ? job.data : undefined,
      // });
    }
  });

  // Note: 'waiting' event không có sẵn trong Worker, chỉ có trong Queue

  // Event khi worker đóng
  worker.on('closed', () => {
    log('warn', `🔒 Worker ${queueName} đã đóng`);
  });

  // Event khi worker pause/resume
  worker.on('paused', () => {
    log('warn', `⏸️ Worker ${queueName} đã tạm dừng`);
  });

  worker.on('resumed', () => {
    log('info', `▶️ Worker ${queueName} đã tiếp tục`);
  });

  return worker;
};

/**
 * Cập nhật trạng thái health của worker
 */
const updateWorkerHealth = (queueName: string, isHealthy: boolean) => {
  const workerManager = workers.get(queueName);
  if (workerManager) {
    workerManager.isHealthy = isHealthy;
    workerManager.lastHealthCheck = new Date();
    
    if (isHealthy) {
      workerManager.failedHealthChecks = 0;
    } else {
      workerManager.failedHealthChecks++;
    }
  }
};

/**
 * Health check cho tất cả workers
 */
const performHealthCheck = async () => {
  for (const [queueName, workerManager] of workers) {
    try {
      // Kiểm tra worker có đang running không
      const isRunning = !workerManager.worker.closing;
      
      if (isRunning) {
        updateWorkerHealth(queueName, true);
      } else {
        updateWorkerHealth(queueName, false);
        log('warn', `Worker ${queueName} không chạy`);
      }
      
      // Kiểm tra xem worker có unhealthy quá lâu không
      if (workerManager.failedHealthChecks >= healthCheckConfig.unhealthyThreshold) {
        log('error', `Worker ${queueName} unhealthy quá lâu, cần restart`);
        // Có thể implement auto-restart logic ở đây
      }
      
    } catch (error) {
      log('error', `Lỗi health check cho worker ${queueName}:`, error);
      updateWorkerHealth(queueName, false);
    }
  }
};

/**
 * Khởi tạo worker cho Product Sync
 */
const createProductSyncWorker = (): Worker => {
  const queueName = QUEUE_NAMES.PRODUCT_SYNC;
  const config = workerConfigs[queueName];

  // Log cấu hình worker (chỉ trong development)
  if (!loggingConfig.healthCheckSilent) {
    log('info', `🔧 Creating worker for ${queueName}`, {
      concurrency: config.concurrency,
      autorun: config.autorun,
    });
  }

  const worker = createWorker(queueName, processProductSyncJob, config);

  workers.set(queueName, {
    worker,
    isHealthy: false,
    lastHealthCheck: new Date(),
    failedHealthChecks: 0,
  });

  return worker;
};

/**
 * Khởi tạo Message Batching Worker
 */
const createMessageBatchingWorker = (): Worker => {
  const queueName = QUEUE_NAMES.MESSAGE_BATCHING;
  const config = workerConfigs[queueName];
  
  const processor = async (job: any) => {
    const jobName = job.name;
    
    switch (jobName) {
      case 'process-message-batch':
        return await processMessageBatch(job);
      default:
        throw new Error(`Unknown job type: ${jobName}`);
    }
  };

  const worker = createWorker(queueName, processor, config);
  
  // Thêm event listeners riêng cho Message Batching
  worker.on('active', (job) => {
    log('info', `⚡ Message Batching Job ${job.id} bắt đầu xử lý`, {
      jobName: job.name,
      batchKey: job.data.batchKey,
      messageCount: job.data.batch?.messages?.length || 0,
      immediate: job.data.immediate,
    });
  });

  worker.on('completed', (job, result) => {
    log('info', `✅ Message Batching Job ${job.id} hoàn thành`, {
      jobName: job.name,
      processed: result.processed,
      immediate: result.immediate,
      duration: Date.now() - job.timestamp,
    });
  });

  worker.on('failed', async (job, error) => {
    // Gọi handler cho failed job
    if (job) {
      try {
        await handleFailedMessageBatch(job, error);
      } catch (handlerError) {
        log('error', `❌ Lỗi khi xử lý failed job ${job.id}:`, handlerError);
      }
    }
  });

  workers.set(queueName, {
    worker,
    isHealthy: false,
    lastHealthCheck: new Date(),
    failedHealthChecks: 0,
  });
  
  return worker;
};

/**
 * Khởi động tất cả workers
 */
export const startAllWorkers = async (): Promise<void> => {
  try {
    log('info', 'Đang khởi động tất cả workers...');
    
    // Khởi tạo Product Sync Worker
    createProductSyncWorker();
    
    // Khởi tạo Message Batching Worker
    createMessageBatchingWorker();
    
    // Bắt đầu health check interval
    setInterval(performHealthCheck, healthCheckConfig.checkInterval);
    
    log('info', `✅ Đã khởi động ${workers.size} workers thành công`);
    
    // Log worker summary
    for (const [queueName] of workers) {
      log('info', `📋 Worker active: ${queueName}`);
    }
    
  } catch (error: any) {
    log('error', '❌ Lỗi khi khởi động workers:', error);
    throw error;
  }
};

/**
 * Dừng tất cả workers
 */
export const stopAllWorkers = async (): Promise<void> => {
  try {
    log('info', 'Đang dừng tất cả workers...');
    
    const shutdownPromises: Promise<void>[] = [];
    
    for (const [queueName, workerManager] of workers) {
      log('info', `Đang dừng worker: ${queueName}`);
      shutdownPromises.push(
        workerManager.worker.close().then(() => {
          log('info', `✅ Worker ${queueName} đã được dừng`);
        })
      );
    }
    
    await Promise.all(shutdownPromises);
    workers.clear();
    
    log('info', '✅ Tất cả workers đã được dừng');
    
  } catch (error: any) {
    log('error', '❌ Lỗi khi dừng workers:', error);
    throw error;
  }
};

/**
 * Lấy worker theo tên queue
 */
export const getWorker = (queueName: string): Worker | null => {
  const workerManager = workers.get(queueName);
  return workerManager ? workerManager.worker : null;
};

/**
 * Lấy worker Product Sync (backward compatibility)
 */
export const getProductSyncWorker = (): Worker | null => {
  return getWorker(QUEUE_NAMES.PRODUCT_SYNC);
};

/**
 * Lấy trạng thái health của tất cả workers
 */
export const getWorkersHealthStatus = () => {
  const status: Record<string, any> = {};
  
  for (const [queueName, workerManager] of workers) {
    status[queueName] = {
      isHealthy: workerManager.isHealthy,
      lastHealthCheck: workerManager.lastHealthCheck,
      failedHealthChecks: workerManager.failedHealthChecks,
      isRunning: !workerManager.worker.closing,
    };
  }
  
  return status;
};

/**
 * Restart worker cụ thể
 */
export const restartWorker = async (queueName: string): Promise<boolean> => {
  try {
    const workerManager = workers.get(queueName);
    if (!workerManager) {
      log('warn', `Không tìm thấy worker: ${queueName}`);
      return false;
    }
    
    log('info', `Đang restart worker: ${queueName}`);
    
    // Dừng worker cũ
    await workerManager.worker.close();
    
    // Tạo worker mới
    let newWorker: Worker;
    
    switch (queueName) {
      case QUEUE_NAMES.PRODUCT_SYNC:
        newWorker = createProductSyncWorker();
        break;
      default:
        log('error', `Không hỗ trợ restart cho worker: ${queueName}`);
        return false;
    }
    
    log('info', `✅ Worker ${queueName} đã được restart thành công`);
    return true;
    
  } catch (error: any) {
    log('error', `❌ Lỗi khi restart worker ${queueName}:`, error);
    return false;
  }
};

// Export default
export default {
  startAllWorkers,
  stopAllWorkers,
  getWorker,
  getProductSyncWorker,
  getWorkersHealthStatus,
  restartWorker,
};
