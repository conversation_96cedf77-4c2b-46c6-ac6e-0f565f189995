import { PostgresStore } from "@mastra/pg";
import dotenv from "dotenv";

// <PERSON><PERSON><PERSON> bảo biến môi trường được load
dotenv.config();

/**
 * Tạo một instance PostgresStore duy nhất để sử dụng trong toàn bộ ứng dụng
 * Điều này giúp tránh cảnh báo về việc tạo nhiều kết nối đến cùng một database
 */
export const postgresStore = new PostgresStore({
  host: process.env.PG_HOST || "localhost",
  port: parseInt(process.env.PG_PORT || "5432"),
  user: process.env.PG_USER || "postgres",
  database: process.env.PG_DATABASE || "postgres",
  password: process.env.PG_PASSWORD || "postgres",
});

// Thêm property resourceWorkingMemory để tương thích với MastraStorage interface
(postgresStore as any).supports = {
  ...postgresStore.supports,
  resourceWorkingMemory: false
};

export default postgresStore;
